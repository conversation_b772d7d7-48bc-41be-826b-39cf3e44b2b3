<template>
  <div class="dept-bi-dashboard" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 侧边栏部门列表 -->
    <div class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <h3>部门列表</h3>
        <el-button
          :icon="sidebarCollapsed ? 'ep:expand' : 'ep:fold'"
          @click="toggleSidebar"
          class="collapse-btn"
          text
        />
      </div>
      <div class="dept-list" v-show="!sidebarCollapsed">
        <div
          v-for="dept in deptList"
          :key="dept.value"
          :class="['dept-item', { active: activeDept === dept.value }]"
          @click="scrollToDept(dept.value)"
        >
          <div class="dept-name">{{ dept.label }}</div>
          <div class="dept-indicator-count"
            >{{ getStrDictOptions(dept.value + '_Indicator').length }}项指标</div
          >
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 顶部筛选区域 -->
      <div class="dashboard-header">
        <h1>部门关键指标看板</h1>
        <div class="filter-section">
          <el-date-picker
            v-model="selectedYear"
            type="year"
            value-format="YYYY"
            format="YYYY"
            placeholder="选择年份"
            @change="loadDeptData"
            class="year-picker !w-100px"
            :clearable="false"
          />
          <el-button @click="loadDeptData" type="primary" :loading="loading"  class="fullscreen-btn">
            <Icon icon="ep:refresh" />
          </el-button>
          <!-- 全屏按钮 -->
          <el-button @click="toggleFullscreen" type="info" class="fullscreen-btn">
            <Icon :icon="isFullscreen ? 'ep:close-bold' : 'ep:full-screen'" />
          </el-button>
        </div>
      </div>

      <!-- 部门看板区域 -->
      <div class="dashboard-content" ref="dashboardContent">
        <div
          v-for="dept in deptList"
          :key="dept.value"
          :id="`dept-${dept.value}`"
          class="dept-section"
        >
          <div class="dept-section-header">
            <h2>{{ dept.label }}</h2>
            <div class="dept-summary">
              <span>{{ getStrDictOptions(dept.value + '_Indicator').length }}项指标</span>
              <span>{{ selectedYear }}年数据</span>
            </div>
          </div>

          <!-- 指标图表 -->
          <div class="charts-container">
            <div
              v-for="indicator in getDeptIndicators(dept.value)"
              :key="`${dept.value}-${indicator.indicator}`"
              class="chart-item"
            >
              <h3>{{ getIndicatorLabel(dept.value, indicator.indicator) }}</h3>
              <div
                :ref="(el) => setChartRef(`${dept.value}-${indicator.indicator}`, el)"
                class="chart"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, computed, watch, markRaw, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import { getStrDictOptions, getDictLabel } from '@/utils/dict'
import { DeptIndicatorApi } from '@/api/hr/deptindicator'
import { dateUtil } from '@/utils/dateUtil'

// 定义数据类型
interface DeptIndicatorData {
  dept: string
  year: number
  month: number
  indicator: string
  value: number
}

interface GroupedIndicatorData {
  [dept: string]: {
    [indicator: string]: DeptIndicatorData[]
  }
}

// 响应式数据
const loading = ref(false)
const sidebarCollapsed = ref(false)
const activeDept = ref('')
const selectedYear = ref(dateUtil().format('YYYY'))
const dashboardContent = ref<HTMLElement>()
const isFullscreen = ref(false)

// 部门列表 - 从字典获取
const deptList = computed(() => getStrDictOptions('first_level_department'))

// 原始指标数据
const indicatorData = ref<DeptIndicatorData[]>([])

// 按部门分组的指标数据
const groupedData = ref<GroupedIndicatorData>({})

// 图表实例存储
const chartInstances = ref<{ [key: string]: echarts.ECharts }>({})

// 图表引用设置函数
const setChartRef = (key: string, el: any) => {
  if (el) {
    // 使用 ResizeObserver 来监听容器尺寸变化
    const observer = new ResizeObserver(() => {
      if (!chartInstances.value[key] && el.offsetWidth > 0 && el.offsetHeight > 0) {
        try {
          // 使用 markRaw 防止 ECharts 实例被 Vue 响应式系统代理
          const chartInstance = markRaw(echarts.init(el as HTMLElement))
          chartInstances.value[key] = chartInstance
          // 如果数据已经加载，立即渲染图表
          const [deptValue, indicator] = key.split('-')
          const indicators = getDeptIndicators(deptValue)
          const indicatorData = indicators.find((item) => item.indicator === indicator)
          if (indicatorData && indicatorData.data.length > 0) {
            renderChart(key, indicatorData.data)
          }
        } catch (error) {
          console.error('图表初始化失败:', error)
        }
      }
    })
    observer.observe(el)

    // 也尝试立即初始化
    nextTick(() => {
      setTimeout(() => {
        if (!chartInstances.value[key] && el.offsetWidth > 0 && el.offsetHeight > 0) {
          try {
            // 使用 markRaw 防止 ECharts 实例被 Vue 响应式系统代理
            const chartInstance = markRaw(echarts.init(el as HTMLElement))
            chartInstances.value[key] = chartInstance
          } catch (error) {
            console.error('图表初始化失败:', error)
          }
        }
      }, 100)
    })
  }
}

// 侧边栏切换
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 容器全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  // 延迟重绘图表以适应新的尺寸，确保DOM更新完成
  nextTick(() => {
    setTimeout(() => {
      handleResize()
    }, 300)
  })
}

// 滚动到指定部门
const scrollToDept = (deptValue: string) => {
  activeDept.value = deptValue
  const element = document.getElementById(`dept-${deptValue}`)
  if (element && dashboardContent.value) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 获取部门指标数据
const getDeptIndicators = (deptValue: string) => {
  const indicators = groupedData.value[deptValue]
  if (!indicators) return []

  return Object.entries(indicators).map(([indicator, data]) => ({
    indicator,
    data
  }))
}

// 获取指标标签
const getIndicatorLabel = (deptValue: string, indicator: string) => {
  return getDictLabel(`${deptValue}_Indicator`, indicator) || indicator
}

// 加载部门数据
const loadDeptData = async () => {
  loading.value = true
  try {
    // 从API获取数据
    const params = {
      year: selectedYear.value
    }
    const data = await DeptIndicatorApi.getDeptIndicatorList(params)
    indicatorData.value = data || []

    // 按部门分组数据
    groupDataByDept()

    // 延迟初始化图表，确保DOM已渲染
    await nextTick()
    setTimeout(() => {
      initCharts()
    }, 500)
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 按部门分组数据
const groupDataByDept = () => {
  const grouped: GroupedIndicatorData = {}

  indicatorData.value.forEach((item) => {
    if (!grouped[item.dept]) {
      grouped[item.dept] = {}
    }
    if (!grouped[item.dept][item.indicator]) {
      grouped[item.dept][item.indicator] = []
    }
    grouped[item.dept][item.indicator].push(item)
  })

  groupedData.value = grouped
}

// 科技感配色方案
const techColors = {
  primary: ['#00D4FF', '#0099CC', '#0066FF', '#3366FF', '#6699FF'],
  gradient: [
    { offset: 0, color: '#00D4FF' },
    { offset: 1, color: '#0066FF' }
  ],
  background: 'rgba(0, 212, 255, 0.1)',
  text: '#00D4FF'
}

// 渲染单个图表
const renderChart = (chartKey: string, data: DeptIndicatorData[]) => {
  const chartInstance = chartInstances.value[chartKey]
  console.log(chartInstance)
  if (!chartInstance) {
    console.warn(`图表实例 ${chartKey} 不存在`)
    return
  }

  // 数据验证
  if (!data || data.length === 0) {
    console.warn(`图表 ${chartKey} 数据为空`)
    // 显示空数据提示
    const emptyOption = {
      backgroundColor: 'transparent',
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#666',
          fontSize: 16
        }
      }
    }
    chartInstance.setOption(emptyOption, true)
    return
  }

  try {
    // 数据排序确保月份顺序正确
    const sortedData = [...data].sort((a, b) => a.month - b.month)

    // 准备图表数据
    const months = sortedData.map((item) => `${item.month}月`)
    const values = sortedData.map((item) => Number(item.value) || 0)
    console.log(months, values)
    // 设置科技感柱状图配置
    const option = {
      backgroundColor: 'transparent',
      title: { show: false },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: techColors.primary[0],
        borderWidth: 1,
        textStyle: { color: '#fff' },
        formatter: (params: any) => {
          if (!params || !params[0]) return ''
          const data = params[0]
          return `${data.name}<br/>${data.seriesName}: ${data.value}`
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '10%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLine: {
          show: true,
          lineStyle: { color: techColors.primary[0] }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        },
        axisTick: {
          show: true,
          lineStyle: { color: techColors.primary[0] }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: { color: techColors.primary[0] }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        },
        axisTick: {
          show: true,
          lineStyle: { color: techColors.primary[0] }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(0, 212, 255, 0.2)',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '指标值',
          type: 'bar',
          // 移除 coordinateSystem 配置，让ECharts自动处理
          data: values,
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 12,
            formatter: '{c}'
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, techColors.gradient),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00FFFF' },
                { offset: 1, color: '#0099FF' }
              ])
            }
          },
          animationDuration: 1000,
          animationEasing: 'cubicOut'
        }
      ]
    }

    // 清除之前的配置并设置新配置
    chartInstance.clear()
    chartInstance.setOption(option, true)

    // 延迟调整大小
    setTimeout(() => {
      if (chartInstance && !chartInstance.isDisposed()) {
        chartInstance.resize()
      }
    }, 100)
  } catch (error) {
    console.error(`图表 ${chartKey} 渲染失败:`, error)
    // 显示错误提示
    const errorOption = {
      backgroundColor: 'transparent',
      title: {
        text: '图表渲染失败',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#ff6b6b',
          fontSize: 16
        }
      }
    }
    chartInstance.setOption(errorOption, true)
  }
}

// 初始化图表
const initCharts = () => {
  // 等待DOM更新完成
  nextTick(() => {
    setTimeout(() => {
      // 为每个部门的每个指标创建图表
      deptList.value.forEach((dept) => {
        const indicators = getDeptIndicators(dept.value)

        indicators.forEach(({ indicator, data }) => {
          const chartKey = `${dept.value}-${indicator}`
          renderChart(chartKey, data)
        })
      })
    }, 200) // 给容器一些时间来获得正确的尺寸
  })
}

// 窗口大小变化时重绘图表
const handleResize = () => {
  // 使用 nextTick 确保在 Vue 更新周期之外调用 resize
  nextTick(() => {
    Object.values(chartInstances.value).forEach((chart) => {
      if (chart && !chart.isDisposed()) {
        try {
          chart.resize()
        } catch (error) {
          console.warn('图表 resize 失败:', error)
        }
      }
    })
  })
}

// 监听数据变化，重新渲染图表
watch(
  groupedData,
  () => {
    nextTick(() => {
      setTimeout(() => {
        initCharts()
      }, 300)
    })
  },
  { deep: true }
)

// ESC键退出容器全屏
const handleKeydown = (event: KeyboardEvent) => {
  // ESC键退出全屏
  if (event.key === 'Escape' && isFullscreen.value) {
    event.preventDefault()
    toggleFullscreen()
  }
}

onMounted(() => {
  loadDeptData()
  window.addEventListener('resize', handleResize)

  // 监听键盘事件
  document.addEventListener('keydown', handleKeydown)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)

  // 清理图表实例
  Object.values(chartInstances.value).forEach((chart) => {
    if (chart && !chart.isDisposed()) {
      chart.dispose()
    }
  })
})
</script>

<style scoped>
.dept-bi-dashboard {
  display: flex;
  height: calc(100vh - 110px);
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  transition: all 0.3s ease;
}

/* 容器全屏模式样式 */
.dept-bi-dashboard.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 20;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
}

/* 全屏模式下隐藏侧边栏 */
/* .dept-bi-dashboard.fullscreen-mode .sidebar {
  display: none;
} */

/* 全屏模式下主内容区域占满整个容器 */
/* .dept-bi-dashboard.fullscreen-mode .main-content {
  width: 100%;
  height: 100%;
  padding: 10px;
} */

/* 全屏模式下调整头部样式 */
.dept-bi-dashboard.fullscreen-mode .dashboard-header {
  margin-bottom: 10px;
  padding: 8px 15px;
}

/* 全屏模式下调整内容区域高度 */
.dept-bi-dashboard.fullscreen-mode .dashboard-content {
  max-height: calc(100% - 60px);
}

/* 全屏模式下调整图表容器 */
.dept-bi-dashboard.fullscreen-mode .charts-container {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 15px;
}

/* 全屏模式下调整图表项 */
.dept-bi-dashboard.fullscreen-mode .chart-item {
  padding: 8px;
}

/* 全屏模式下调整图表高度 */
.dept-bi-dashboard.fullscreen-mode .chart {
  height: 280px;
  min-height: 280px;
}

/* 侧边栏样式 */
.sidebar {
  width: 200px;
  background: linear-gradient(180deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 102, 255, 0.05) 100%);
  border-right: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  overflow: hidden;
}

.sidebar-collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.sidebar-header h3 {
  margin: 0;
  color: #00d4ff;
  font-size: 18px;
  font-weight: 600;
}

.collapse-btn {
  color: #00d4ff !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  background: rgba(0, 212, 255, 0.1) !important;
}

.collapse-btn:hover {
  background: rgba(0, 212, 255, 0.2) !important;
  border-color: #00d4ff !important;
}

.dept-list {
  padding: 10px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.dept-item {
  padding: 15px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  background: rgba(0, 212, 255, 0.05);
}

.dept-item:hover {
  background: rgba(0, 212, 255, 0.15);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateX(5px);
}

.dept-item.active {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 102, 255, 0.1) 100%);
  border-color: #00d4ff;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.dept-name {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 5px;
}

.dept-indicator-count {
  font-size: 12px;
  color: #00d4ff;
  opacity: 0.8;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
}

.dashboard-header h1 {
  margin: 0;
  color: #00d4ff;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.filter-section {
  display: flex;
  gap: 15px;
  align-items: center;
}

.year-picker {
  background: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  border-radius: 8px !important;
}

.fullscreen-btn {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 102, 255, 0.1) 100%) !important;
  border: 1px solid rgba(0, 212, 255, 0.3) !important;
  color: #00d4ff !important;
  transition: all 0.3s ease !important;
}

.fullscreen-btn:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 102, 255, 0.2) 100%) !important;
  border-color: #00d4ff !important;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3) !important;
  transform: translateY(-2px) !important;
}

/* 部门看板区域 */
.dashboard-content {
  max-height: calc(100% - 70px);
  overflow-y: auto;
  padding-right: 10px;
}

.dept-section {
  margin-bottom: 40px;
  padding: 10px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(0, 102, 255, 0.02) 100%);
  border-radius: 16px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
}

.dept-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.dept-section-header h2 {
  margin: 0;
  color: #00d4ff;
  font-size: 24px;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

.dept-summary {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-item {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 212, 255, 0.05) 100%);
  border-radius: 12px;
  padding: 10px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
}

.chart-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 212, 255, 0.2);
  border-color: #00d4ff;
}

.chart-item h3 {
  margin: 0 0 15px 0;
  color: #00d4ff;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.chart {
  height: 300px;
  min-height: 300px;
  width: 100%;
  min-width: 300px;
  border-radius: 8px;
  position: relative;
}

/* 滚动条样式 */
.dashboard-content::-webkit-scrollbar,
.dept-list::-webkit-scrollbar {
  width: 6px;
}

.dashboard-content::-webkit-scrollbar-track,
.dept-list::-webkit-scrollbar-track {
  background: rgba(0, 212, 255, 0.1);
  border-radius: 3px;
}

.dashboard-content::-webkit-scrollbar-thumb,
.dept-list::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00d4ff 0%, #0066ff 100%);
  border-radius: 3px;
}

.dashboard-content::-webkit-scrollbar-thumb:hover,
.dept-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00ffff 0%, #0099ff 100%);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dept-bi-dashboard {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  }

  .sidebar-collapsed {
    width: 100%;
    height: 60px;
  }

  .dept-list {
    display: flex;
    overflow-x: auto;
    padding: 10px;
    gap: 10px;
  }

  .dept-item {
    min-width: 120px;
    text-align: center;
    margin-bottom: 0;
  }

  .main-content {
    padding: 15px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-header h1 {
    font-size: 22px;
  }

  .filter-section {
    justify-content: center;
  }

  .charts-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .chart-item {
    padding: 15px;
  }

  .chart {
    height: 250px;
    min-height: 250px;
    min-width: 250px;
  }

  .dept-section {
    padding: 10px;
    margin-bottom: 20px;
  }

  .dept-section-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .dept-summary {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .charts-container {
    grid-template-columns: 1fr;
  }

  .chart-item {
    padding: 10px;
  }

  .chart {
    height: 200px;
    min-height: 200px;
    min-width: 200px;
  }

  .dashboard-header h1 {
    font-size: 18px;
  }

  .dept-section-header h2 {
    font-size: 20px;
  }
}

/* 动画效果 */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.2);
  }
}

.dept-item.active {
  animation: glow 2s infinite;
}
</style>
